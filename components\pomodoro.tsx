"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { getTranslations } from "@/lib/i18n/translations"
import { Play, Pause, RefreshCw, Settings } from "lucide-react"
import { useTimerAudio } from "@/hooks/use-robust-audio"
import { useRTL } from "@/hooks/useRTL"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"

interface PomodoroProps {
  lang: string
}

type PomodoroPhase = "work" | "shortBreak" | "longBreak"

interface PomodoroSettings {
  workDuration: number
  shortBreakDuration: number
  longBreakDuration: number
  longBreakInterval: number
}

// Clés de stockage pour les paramètres Pomodoro
const POMODORO_SETTINGS_KEY = "timetools_pomodoro_settings"
const POMODORO_STATE_KEY = "timetools_pomodoro_state"

export function Pomodoro({ lang }: PomodoroProps) {
  const t = getTranslations(lang)
  const isRTL = useRTL(lang)
  const [isRunning, setIsRunning] = useState(false)
  const [currentPhase, setCurrentPhase] = useState<PomodoroPhase>("work")
  const [timeLeft, setTimeLeft] = useState(25 * 60) // 25 minutes in seconds
  const [completedPomodoros, setCompletedPomodoros] = useState(0)
  const [showSettings, setShowSettings] = useState(false)
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)
  const [settings, setSettings] = useState<PomodoroSettings>({
    workDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    longBreakInterval: 4,
  })
  // const [lastUpdateTime, setLastUpdateTime] = useState<number | null>(null) // No longer needed with endTimeRef

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const endTimeRef = useRef<number>(0) // Timestamp when the current phase should end
  const originalTitle = useRef<string>("")

  // Utiliser le hook useSound
  const { playTimerCompletionSound } = useTimerAudio()

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title
    return () => {
      document.title = originalTitle.current
    }
  }, [])

  // Mettre à jour le titre de l'onglet du navigateur avec le temps et la phase
  useEffect(() => {
    if (isRunning) {
      const phaseName = getPhaseName()
      document.title = `${formatTime(timeLeft)} - ${phaseName} - ${t.pomodoro || "Pomodoro"}`
    } else {
      document.title = originalTitle.current
    }
  }, [timeLeft, isRunning, currentPhase, t])

  // Charger les paramètres et l'état sauvegardés
  useEffect(() => {
    const savedSettings = getFromLocalStorage<PomodoroSettings>(POMODORO_SETTINGS_KEY, {
      workDuration: 25,
      shortBreakDuration: 5,
      longBreakDuration: 15,
      longBreakInterval: 4,
    })

    setSettings(savedSettings)

    const savedState = getFromLocalStorage<{
      phase: PomodoroPhase
      timeLeft: number
      completedPomodoros: number
      isRunning: boolean
      lastUpdateTime: number | null
    } | null>(POMODORO_STATE_KEY, null)

    if (savedState) {
      setCurrentPhase(savedState.phase)
      setCompletedPomodoros(savedState.completedPomodoros)

      // Si le timer était en cours d'exécution, calculer le temps écoulé depuis la dernière mise à jour
      if (savedState.isRunning && savedState.lastUpdateTime) {
        const now = Date.now()
        const elapsedSeconds = Math.floor((now - savedState.lastUpdateTime) / 1000)
        const newTimeLeft = Math.max(0, savedState.timeLeft - elapsedSeconds)

        setTimeLeft(newTimeLeft)
        // Ne pas démarrer automatiquement le timer, mais conserver l'état
        setIsRunning(false)
      } else {
        setTimeLeft(savedState.timeLeft)
        setIsRunning(false)
      }
    } else {
      // Initialiser avec les paramètres par défaut
      setTimeLeft(savedSettings.workDuration * 60)
    }
  }, [])

  // Sauvegarder les paramètres lorsqu'ils changent
  useEffect(() => {
    saveToLocalStorage(POMODORO_SETTINGS_KEY, settings)
  }, [settings])

  // Sauvegarder l'état actuel périodiquement et lors des changements importants
  useEffect(() => {
    const saveState = () => {
      // Calculate remaining time accurately if running before saving
      let timeToSave = timeLeft;
      if (isRunning && endTimeRef.current > 0) {
        const remainingMs = Math.max(0, endTimeRef.current - Date.now());
        timeToSave = Math.floor(remainingMs / 1000);
      }

      const state = {
        phase: currentPhase,
        timeLeft: timeToSave, // Save the accurately calculated time
        completedPomodoros,
        isRunning,
        // lastUpdateTime: isRunning ? Date.now() : null, // No longer needed
      }
      saveToLocalStorage(POMODORO_STATE_KEY, state)
    }

    // Sauvegarder immédiatement lors des changements d'état
    saveState()

    // Sauvegarder périodiquement si le timer est en cours
    let saveInterval: NodeJS.Timeout | null = null
    if (isRunning) {
      saveInterval = setInterval(saveState, 10000) // Sauvegarder toutes les 10 secondes
    }

    return () => {
      if (saveInterval) clearInterval(saveInterval)
    }
  }, [currentPhase, timeLeft, completedPomodoros, isRunning])

  // Timer logic using timestamps
  useEffect(() => {
    if (isRunning) {
      // Calculate end time ONLY if starting (or resuming after reset/phase change)
      // If endTimeRef.current is 0, it means we need a new target time.
      if (endTimeRef.current === 0) {
        const validTimeLeft = Math.max(0, timeLeft);
        endTimeRef.current = Date.now() + validTimeLeft * 1000;
      }
      // If resuming from pause, endTimeRef.current should already be set correctly relative to the original start.

      timerRef.current = setInterval(() => {
        const now = Date.now();
        const remainingMs = endTimeRef.current - now;

        if (remainingMs <= 0) {
            // Timer completed
            clearInterval(timerRef.current!)
            timerRef.current = null;
            setTimeLeft(0); // Explicitly set to 0

            // Play sound immediately when phase ends with robust audio
            try {
              playTimerCompletionSound("bell")
            } catch (error) {
              console.warn("Could not play timer completion sound:", error)
            }

            // Determine next phase and time AFTER sound
            let nextPhase: PomodoroPhase = "work";
            let nextTime = settings.workDuration * 60;
            let newCompletedPomodoros = completedPomodoros;

            if (currentPhase === "work") {
              newCompletedPomodoros = completedPomodoros + 1;
              setCompletedPomodoros(newCompletedPomodoros);

              if (newCompletedPomodoros % settings.longBreakInterval === 0) {
                nextPhase = "longBreak";
                nextTime = settings.longBreakDuration * 60;
              } else {
                nextPhase = "shortBreak";
                nextTime = settings.shortBreakDuration * 60;
              }
            } else {
              // After break, always go back to work
              nextPhase = "work";
              nextTime = settings.workDuration * 60;
            }

            setCurrentPhase(nextPhase);
            setTimeLeft(nextTime); // Set time for the next phase
            setIsRunning(false); // Stop the timer after completion
            endTimeRef.current = 0; // Reset end time ref

         } else {
            // Update display time directly using Math.round
            const newTimeLeftSeconds = Math.round(remainingMs / 1000);
            setTimeLeft(newTimeLeftSeconds);
         }
       }, 250); // Check frequently

    } else {
       // When pausing, clear interval and store remaining time accurately
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
         // Calculate and store remaining time based on endTimeRef
         // Use Math.round for better accuracy near second boundaries
         // Only update if endTimeRef was actually set (timer had run)
         if (endTimeRef.current > 0) {
            const remainingMs = Math.max(0, endTimeRef.current - Date.now());
            setTimeLeft(Math.round(remainingMs / 1000));
         }
         // Don't reset endTimeRef.current here; preserve it for resume
       }
     }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
    // Dependencies: isRunning triggers start/pause.
    // Other dependencies handle phase changes, settings updates, and sound playback.
    // timeLeft is intentionally omitted as the interval calculates remaining time based on endTimeRef.
  }, [isRunning, currentPhase, completedPomodoros, settings, playSound]);


  const resetTimer = () => {
    setIsRunning(false)
    setCurrentPhase("work")
    const newTimeLeft = settings.workDuration * 60;
    setTimeLeft(newTimeLeft)
    setCompletedPomodoros(0)
    endTimeRef.current = 0; // Reset end time ref
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null;
    }

    // Restaurer le titre original
    document.title = originalTitle.current
  }

  const changePhase = (phase: PomodoroPhase) => {
    // Jouer un son lors du changement manuel de phase
    if (currentPhase !== phase) {
      setTimeout(() => {
        try {
          playTimerCompletionSound("bell")
        } catch (error) {
          console.warn("Could not play timer completion sound:", error)
        }
      }, 10)
    }

    setCurrentPhase(phase)

    // Determine the time for the selected phase
    let newTimeLeft = 0;
    if (phase === "work") {
      newTimeLeft = settings.workDuration * 60;
    } else if (phase === "shortBreak") {
      newTimeLeft = settings.shortBreakDuration * 60;
    } else if (phase === "longBreak") {
      newTimeLeft = settings.longBreakDuration * 60;
    }
    setTimeLeft(newTimeLeft);

    // Stop the timer and reset end time when manually changing phase
    setIsRunning(false);
    endTimeRef.current = 0;
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }

  const toggleTimer = () => {
    // If timer is finished (timeLeft is 0) and we press play, reset to current phase duration
    if (!isRunning && timeLeft <= 0) {
       let phaseDuration = 0;
       if (currentPhase === "work") phaseDuration = settings.workDuration * 60;
       else if (currentPhase === "shortBreak") phaseDuration = settings.shortBreakDuration * 60;
       else phaseDuration = settings.longBreakDuration * 60;
       setTimeLeft(phaseDuration);
       // Use a small timeout to allow state update before starting
       setTimeout(() => setIsRunning(true), 50);
    } else {
       setIsRunning(!isRunning);
    }
    // endTimeRef will be calculated in the useEffect when isRunning becomes true
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const getPhaseColor = () => {
    switch (currentPhase) {
      case "work":
        return "bg-red-500"
      case "shortBreak":
        return "bg-green-500"
      case "longBreak":
        return "bg-blue-500"
    }
  }

  const getPhaseName = () => {
    switch (currentPhase) {
      case "work":
        return t.workPhase
      case "shortBreak":
        return t.shortBreakPhase
      case "longBreak":
        return t.longBreakPhase
    }
  }

  const getTotalTime = () => {
    switch (currentPhase) {
      case "work":
        return settings.workDuration * 60
      case "shortBreak":
        return settings.shortBreakDuration * 60
      case "longBreak":
        return settings.longBreakDuration * 60
    }
  }

  const progress = (timeLeft / getTotalTime()) * 100

  // Fonction utilitaire pour sauvegarder dans localStorage
  function saveToLocalStorage<T>(key: string, data: T): void {
    try {
      if (typeof window !== "undefined") {
        const serializedData = JSON.stringify(data)
        localStorage.setItem(key, serializedData)
      }
    } catch (error) {
      console.error("Error saving to localStorage:", error)
    }
  }

  // Fonction utilitaire pour charger depuis localStorage
  function getFromLocalStorage<T>(key: string, defaultValue: T): T {
    try {
      if (typeof window !== "undefined") {
        const serializedData = localStorage.getItem(key)
        if (serializedData === null) {
          return defaultValue
        }
        return JSON.parse(serializedData) as T
      }
      return defaultValue
    } catch (error) {
      console.error("Error getting from localStorage:", error)
      return defaultValue
    }
  }

  const pomodoroContent = (
    <>
      {showSettings ? (
        <div className="space-y-6">
          <h3 className="font-medium text-lg">{t.settings}</h3>

          <div className="space-y-4">
            <div>
              <Label htmlFor="work-duration" className="mb-2 block">
                {t.workDuration}: {settings.workDuration} {t.minutes}
              </Label>
              <Slider
                id="work-duration"
                min={1}
                max={60}
                step={1}
                value={[settings.workDuration]}
                onValueChange={(value) => setSettings({ ...settings, workDuration: value[0] })}
                dir={isRTL ? "rtl" : "ltr"}
                data-rtl-slider={isRTL ? "true" : "false"}
              />
            </div>

            <div>
              <Label htmlFor="short-break" className="mb-2 block">
                {t.shortBreak}: {settings.shortBreakDuration} {t.minutes}
              </Label>
              <Slider
                id="short-break"
                min={1}
                max={15}
                step={1}
                value={[settings.shortBreakDuration]}
                onValueChange={(value) => setSettings({ ...settings, shortBreakDuration: value[0] })}
                dir={isRTL ? "rtl" : "ltr"}
                data-rtl-slider={isRTL ? "true" : "false"}
              />
            </div>

            <div>
              <Label htmlFor="long-break" className="mb-2 block">
                {t.longBreak}: {settings.longBreakDuration} {t.minutes}
              </Label>
              <Slider
                id="long-break"
                min={5}
                max={30}
                step={1}
                value={[settings.longBreakDuration]}
                onValueChange={(value) => setSettings({ ...settings, longBreakDuration: value[0] })}
                dir={isRTL ? "rtl" : "ltr"}
                data-rtl-slider={isRTL ? "true" : "false"}
              />
            </div>

            <div>
              <Label htmlFor="long-break-interval" className="mb-2 block">
                {t.longBreakInterval}: {t.every} {settings.longBreakInterval} {t.pomodoros}
              </Label>
              <Slider
                id="long-break-interval"
                min={2}
                max={8}
                step={1}
                value={[settings.longBreakInterval]}
                onValueChange={(value) => setSettings({ ...settings, longBreakInterval: value[0] })}
                dir={isRTL ? "rtl" : "ltr"}
                data-rtl-slider={isRTL ? "true" : "false"}
              />
            </div>
          </div>

          <Button onClick={() => {
            // Mettre à jour le temps restant en fonction de la phase actuelle
            let newTimeLeft = timeLeft;

            // Si le timer n'est pas en cours d'exécution ou est terminé, réinitialiser le temps
            if (!isRunning || timeLeft === 0) {
              if (currentPhase === "work") {
                newTimeLeft = settings.workDuration * 60;
              } else if (currentPhase === "shortBreak") {
                newTimeLeft = settings.shortBreakDuration * 60;
              } else if (currentPhase === "longBreak") {
                newTimeLeft = settings.longBreakDuration * 60;
              }
              setTimeLeft(newTimeLeft);
            } else {
              // Si le timer est en cours d'exécution, ajuster le temps restant proportionnellement
              const totalTime = getTotalTime();
              const progressPercentage = timeLeft / totalTime;

              let newTotalTime;
              if (currentPhase === "work") {
                newTotalTime = settings.workDuration * 60;
              } else if (currentPhase === "shortBreak") {
                newTotalTime = settings.shortBreakDuration * 60;
              } else {
                newTotalTime = settings.longBreakDuration * 60;
              }

              // Calculer le nouveau temps restant en conservant le même pourcentage de progression
              newTimeLeft = Math.round(progressPercentage * newTotalTime);
              setTimeLeft(newTimeLeft);

              // Réinitialiser la référence de temps de fin pour le nouveau temps
              endTimeRef.current = Date.now() + newTimeLeft * 1000;
            }

            // Masquer les paramètres
            setShowSettings(false);
          }} className="w-full">
            {t.save}
          </Button>
        </div>
      ) : (
        <>
          <Tabs defaultValue={currentPhase} value={currentPhase} className="mb-6">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="work" onClick={() => changePhase("work")} className="text-xs sm:text-sm md:text-base">
                {t.workPhase}
              </TabsTrigger>
              <TabsTrigger value="shortBreak" onClick={() => changePhase("shortBreak")} className="text-xs sm:text-sm md:text-base">
                {t.shortBreakPhase}
              </TabsTrigger>
              <TabsTrigger value="longBreak" onClick={() => changePhase("longBreak")} className="text-xs sm:text-sm md:text-base">
                {t.longBreakPhase}
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="text-center mb-4">
            <p className="text-sm text-muted-foreground">
              {t.completedPomodoros}: {completedPomodoros}
            </p>
            <h2 className="text-xl font-medium">{getPhaseName()}</h2>
          </div>

          <div className="w-full h-3 bg-muted rounded-full mb-8 overflow-hidden">
            <motion.div
              className={`h-full ${getPhaseColor()}`}
              style={{ width: `${progress}%` }}
              initial={{ width: "100%" }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>

          <div className="text-7xl md:text-8xl font-mono font-bold text-center mb-12">{formatTime(timeLeft)}</div>

          <div className="flex justify-center gap-6">
            <Button
              onClick={toggleTimer}
              size="lg"
              className="w-24 h-24 rounded-full"
              variant={isRunning ? "destructive" : "default"}
            >
              {isRunning ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
            </Button>

            <Button onClick={resetTimer} size="lg" variant="outline" className="w-20 h-20 rounded-full">
              <RefreshCw className="h-7 w-7" />
            </Button>
          </div>
        </>
      )}
    </>
  )

  return (
    <>
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>{t.pomodoro}</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={() => setShowSettings(!showSettings)}>
                <Settings className="h-5 w-5" />
                <span className="sr-only">{t.settings}</span>
              </Button>
              <ToolControls
                onFullscreenToggle={setIsToolFullscreen}
                variant="compact"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {pomodoroContent}
        </CardContent>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.pomodoro}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {pomodoroContent}
          </CardContent>
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
