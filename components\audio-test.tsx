"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTimerAudio, useNotificationAudio } from "@/hooks/use-robust-audio"
import { Volume2, VolumeX, Smartphone, Monitor, Wifi, WifiOff } from "lucide-react"

export function AudioTest() {
  const [testResults, setTestResults] = useState<string[]>([])
  const timerAudio = useTimerAudio()
  const notificationAudio = useNotificationAudio()

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const testBasicSounds = async () => {
    addTestResult("Testing basic sounds...")
    
    try {
      await timerAudio.playBell()
      addTestResult("✅ Bell sound played successfully")
    } catch (error) {
      addTestResult(`❌ Bell sound failed: ${error}`)
    }

    setTimeout(async () => {
      try {
        await timerAudio.playAlarm()
        addTestResult("✅ Alarm sound played successfully")
      } catch (error) {
        addTestResult(`❌ Alarm sound failed: ${error}`)
      }
    }, 1000)

    setTimeout(async () => {
      try {
        await timerAudio.playNotification()
        addTestResult("✅ Notification sound played successfully")
      } catch (error) {
        addTestResult(`❌ Notification sound failed: ${error}`)
      }
    }, 2000)
  }

  const testTimerCompletionSounds = async () => {
    addTestResult("Testing timer completion sounds...")
    
    try {
      await timerAudio.playTimerBell()
      addTestResult("✅ Timer bell played successfully")
    } catch (error) {
      addTestResult(`❌ Timer bell failed: ${error}`)
    }

    setTimeout(async () => {
      try {
        await timerAudio.playTimerAlarm()
        addTestResult("✅ Timer alarm played successfully")
      } catch (error) {
        addTestResult(`❌ Timer alarm failed: ${error}`)
      }
    }, 1500)
  }

  const testRepeatedSounds = async () => {
    addTestResult("Testing repeated sounds...")
    
    try {
      await timerAudio.playRepeatedBell(3, 800)
      addTestResult("✅ Repeated bell sequence played successfully")
    } catch (error) {
      addTestResult(`❌ Repeated bell sequence failed: ${error}`)
    }
  }

  const testWakeLock = async () => {
    addTestResult("Testing Wake Lock...")
    
    try {
      await timerAudio.requestWakeLock()
      addTestResult("✅ Wake Lock requested successfully")
      
      setTimeout(() => {
        timerAudio.releaseWakeLock()
        addTestResult("✅ Wake Lock released successfully")
      }, 3000)
    } catch (error) {
      addTestResult(`❌ Wake Lock failed: ${error}`)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Test du Système Audio Robuste
          </CardTitle>
          <CardDescription>
            Testez les différentes fonctionnalités du système audio pour mobile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* État du système */}
          <div className="flex flex-wrap gap-2">
            <Badge variant={timerAudio.isSoundEnabled ? "default" : "secondary"}>
              {timerAudio.isSoundEnabled ? (
                <>
                  <Volume2 className="h-3 w-3 mr-1" />
                  Son activé
                </>
              ) : (
                <>
                  <VolumeX className="h-3 w-3 mr-1" />
                  Son désactivé
                </>
              )}
            </Badge>
            <Badge variant={timerAudio.isAudioReady ? "default" : "secondary"}>
              {timerAudio.isAudioReady ? (
                <>
                  <Wifi className="h-3 w-3 mr-1" />
                  Audio prêt
                </>
              ) : (
                <>
                  <WifiOff className="h-3 w-3 mr-1" />
                  Audio en cours d'initialisation
                </>
              )}
            </Badge>
            <Badge variant="outline">
              <Smartphone className="h-3 w-3 mr-1" />
              Mobile optimisé
            </Badge>
          </div>

          {/* Contrôles de test */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button 
              onClick={testBasicSounds}
              disabled={!timerAudio.isAudioReady}
              size="sm"
            >
              Sons de base
            </Button>
            <Button 
              onClick={testTimerCompletionSounds}
              disabled={!timerAudio.isAudioReady}
              size="sm"
            >
              Sons de minuterie
            </Button>
            <Button 
              onClick={testRepeatedSounds}
              disabled={!timerAudio.isAudioReady}
              size="sm"
            >
              Sons répétés
            </Button>
            <Button 
              onClick={testWakeLock}
              disabled={!timerAudio.isAudioReady}
              size="sm"
            >
              Wake Lock
            </Button>
          </div>

          {/* Sons individuels */}
          <div className="grid grid-cols-3 gap-2">
            <Button 
              onClick={timerAudio.playBell}
              disabled={!timerAudio.isAudioReady}
              variant="outline"
              size="sm"
            >
              🔔 Cloche
            </Button>
            <Button 
              onClick={timerAudio.playAlarm}
              disabled={!timerAudio.isAudioReady}
              variant="outline"
              size="sm"
            >
              🚨 Alarme
            </Button>
            <Button 
              onClick={timerAudio.playNotification}
              disabled={!timerAudio.isAudioReady}
              variant="outline"
              size="sm"
            >
              📢 Notification
            </Button>
          </div>

          {/* Contrôle du son */}
          <div className="flex gap-2">
            <Button 
              onClick={timerAudio.toggleSound}
              variant="outline"
              size="sm"
            >
              {timerAudio.isSoundEnabled ? "Désactiver le son" : "Activer le son"}
            </Button>
            <Button 
              onClick={clearResults}
              variant="outline"
              size="sm"
            >
              Effacer les résultats
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Résultats des tests */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Résultats des tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 max-h-60 overflow-y-auto">
              {testResults.map((result, index) => (
                <div 
                  key={index} 
                  className="text-xs font-mono p-2 bg-muted rounded"
                >
                  {result}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Instructions de test mobile</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <p>1. <strong>Testez avec l'écran déverrouillé</strong> : Tous les sons doivent fonctionner normalement</p>
          <p>2. <strong>Verrouillez l'écran</strong> et testez les sons de minuterie</p>
          <p>3. <strong>Mettez l'application en arrière-plan</strong> et testez</p>
          <p>4. <strong>Testez avec différents navigateurs</strong> (Safari, Chrome, Firefox)</p>
          <p>5. <strong>Vérifiez le Wake Lock</strong> : l'écran doit rester actif pendant les tests</p>
        </CardContent>
      </Card>
    </div>
  )
}
