"use client"

import { useEffect, useCallback } from "react"
import { useSound } from "@/components/sound-provider"

type SoundType = "bell" | "alarm" | "notification"

interface UseRobustAudioOptions {
  enableWakeLock?: boolean
  autoInitialize?: boolean
}

export function useRobustAudio(options: UseRobustAudioOptions = {}) {
  const { 
    playSound, 
    isSoundEnabled, 
    toggleSound, 
    isAudioReady, 
    requestWakeLock, 
    releaseWakeLock 
  } = useSound()

  const { enableWakeLock = true, autoInitialize = true } = options

  // Fonction pour jouer un son avec gestion automatique du Wake Lock
  const playRobustSound = useCallback(async (type: SoundType) => {
    if (!isSoundEnabled) {
      console.log(`Sound disabled, skipping ${type} sound`)
      return
    }

    try {
      // Demander Wake Lock si activé
      if (enableWakeLock) {
        await requestWakeLock()
      }

      // <PERSON><PERSON> le son
      await playSound(type)

      console.log(`Robust sound played successfully: ${type}`)
    } catch (error) {
      console.error(`Failed to play robust sound ${type}:`, error)
    }
  }, [playSound, isSoundEnabled, enableWakeLock, requestWakeLock])

  // Fonction pour jouer un son de fin de minuterie avec Wake Lock prolongé
  const playTimerCompletionSound = useCallback(async (type: SoundType = "bell") => {
    if (!isSoundEnabled) return

    try {
      // Demander Wake Lock pour maintenir l'activité plus longtemps
      if (enableWakeLock) {
        await requestWakeLock()
      }

      // Jouer le son
      await playSound(type)

      // Maintenir le Wake Lock plus longtemps pour les minuteries
      if (enableWakeLock) {
        setTimeout(() => {
          releaseWakeLock()
        }, 5000) // 5 secondes pour les minuteries
      }

      console.log(`Timer completion sound played: ${type}`)
    } catch (error) {
      console.error(`Failed to play timer completion sound:`, error)
    }
  }, [playSound, isSoundEnabled, enableWakeLock, requestWakeLock, releaseWakeLock])

  // Fonction pour jouer plusieurs sons (pour les alarmes répétées)
  const playRepeatedSound = useCallback(async (
    type: SoundType = "alarm", 
    count: number = 3, 
    interval: number = 1000
  ) => {
    if (!isSoundEnabled || count <= 0) return

    try {
      // Demander Wake Lock pour toute la séquence
      if (enableWakeLock) {
        await requestWakeLock()
      }

      for (let i = 0; i < count; i++) {
        await playSound(type)
        
        // Attendre entre les sons (sauf pour le dernier)
        if (i < count - 1) {
          await new Promise(resolve => setTimeout(resolve, interval))
        }
      }

      // Libérer le Wake Lock après la séquence
      if (enableWakeLock) {
        setTimeout(() => {
          releaseWakeLock()
        }, 2000)
      }

      console.log(`Repeated sound sequence completed: ${count} x ${type}`)
    } catch (error) {
      console.error(`Failed to play repeated sound sequence:`, error)
    }
  }, [playSound, isSoundEnabled, enableWakeLock, requestWakeLock, releaseWakeLock])

  // Initialisation automatique du Wake Lock si demandé
  useEffect(() => {
    if (autoInitialize && enableWakeLock && isAudioReady) {
      // Demander Wake Lock lors de l'initialisation pour s'assurer qu'il est disponible
      requestWakeLock().then(() => {
        // Libérer immédiatement, juste pour tester la disponibilité
        setTimeout(releaseWakeLock, 100)
      }).catch((error) => {
        console.warn("Wake Lock not available:", error)
      })
    }
  }, [autoInitialize, enableWakeLock, isAudioReady, requestWakeLock, releaseWakeLock])

  return {
    // Fonctions de base
    playSound: playRobustSound,
    playTimerCompletionSound,
    playRepeatedSound,
    
    // État
    isSoundEnabled,
    isAudioReady,
    
    // Contrôles
    toggleSound,
    requestWakeLock,
    releaseWakeLock,
    
    // Fonctions de commodité
    playBell: () => playRobustSound("bell"),
    playAlarm: () => playRobustSound("alarm"),
    playNotification: () => playRobustSound("notification"),
    
    // Fonctions spécialisées pour minuteries
    playTimerBell: () => playTimerCompletionSound("bell"),
    playTimerAlarm: () => playTimerCompletionSound("alarm"),
    
    // Fonctions pour alarmes répétées
    playRepeatedBell: (count?: number, interval?: number) => playRepeatedSound("bell", count, interval),
    playRepeatedAlarm: (count?: number, interval?: number) => playRepeatedSound("alarm", count, interval)
  }
}

// Hook spécialisé pour les minuteries
export function useTimerAudio() {
  return useRobustAudio({ 
    enableWakeLock: true, 
    autoInitialize: true 
  })
}

// Hook spécialisé pour les notifications simples
export function useNotificationAudio() {
  return useRobustAudio({ 
    enableWakeLock: false, 
    autoInitialize: false 
  })
}
