"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useRef, useCallback } from "react"

type SoundType = "bell" | "alarm" | "notification"

interface SoundContextType {
  playSound: (type: SoundType) => Promise<void>
  isSoundEnabled: boolean
  toggleSound: () => void
  isAudioReady: boolean
  requestWakeLock: () => Promise<void>
  releaseWakeLock: () => void
}

const SoundContext = createContext<SoundContextType | null>(null)

// Détection mobile améliorée
const isMobileDevice = () => {
  if (typeof window === "undefined") return false
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         (navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /MacIntel/.test(navigator.platform))
}

// Détection iOS spécifique
const isIOSDevice = () => {
  if (typeof window === "undefined") return false
  return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
         (navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /MacIntel/.test(navigator.platform))
}

export function SoundProvider({ children }: { children: React.ReactNode }) {
  const [isSoundEnabled, setIsSoundEnabled] = useState(true)
  const [isAudioReady, setIsAudioReady] = useState(false)
  const audioContextRef = useRef<AudioContext | null>(null)
  const audioBuffersRef = useRef<Map<SoundType, AudioBuffer>>(new Map())
  const preloadedAudioRef = useRef<Map<SoundType, HTMLAudioElement>>(new Map())
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)
  const isInitializedRef = useRef(false)

  // Wake Lock pour maintenir l'activité de l'application
  const requestWakeLock = useCallback(async () => {
    if (!('wakeLock' in navigator) || wakeLockRef.current) return

    try {
      wakeLockRef.current = await navigator.wakeLock.request('screen')
      console.log('Wake Lock acquired successfully')

      wakeLockRef.current.addEventListener('release', () => {
        console.log('Wake Lock was released')
        wakeLockRef.current = null
      })
    } catch (error) {
      console.warn('Failed to acquire Wake Lock:', error)
    }
  }, [])

  const releaseWakeLock = useCallback(() => {
    if (wakeLockRef.current) {
      wakeLockRef.current.release()
      wakeLockRef.current = null
      console.log('Wake Lock released manually')
    }
  }, [])

  // Initialiser le contexte audio avec optimisations mobiles
  const initAudioContext = useCallback(() => {
    if (typeof window === "undefined" || audioContextRef.current) return

    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (AudioContextClass) {
        // Configuration optimisée pour mobile
        const contextOptions: AudioContextOptions = {}
        if (isMobileDevice()) {
          contextOptions.sampleRate = 44100 // Taux d'échantillonnage standard
          contextOptions.latencyHint = 'interactive' // Latence minimale
        }

        audioContextRef.current = new AudioContextClass(contextOptions)
        console.log("AudioContext initialized successfully", {
          sampleRate: audioContextRef.current.sampleRate,
          state: audioContextRef.current.state
        })
      } else {
        console.warn("AudioContext not supported in this browser")
      }
    } catch (error) {
      console.error("Failed to initialize AudioContext:", error)
    }
  }, [])

  // Précharger les fichiers audio
  const preloadAudioFiles = useCallback(async () => {
    const audioFiles: Record<SoundType, string> = {
      bell: "/sound/bell.mp3",
      alarm: "/sound/bell.mp3", // Utilise le même fichier pour l'instant
      notification: "/sound/bell.mp3" // Utilise le même fichier pour l'instant
    }

    for (const [type, url] of Object.entries(audioFiles)) {
      try {
        // Précharger avec HTMLAudioElement pour compatibilité mobile
        const audio = new Audio()
        audio.preload = 'auto'
        audio.crossOrigin = 'anonymous'

        // Configuration spéciale pour mobile
        if (isMobileDevice()) {
          audio.volume = 1.0
          audio.muted = false
        }

        await new Promise<void>((resolve, reject) => {
          audio.oncanplaythrough = () => resolve()
          audio.onerror = () => reject(new Error(`Failed to load ${url}`))
          audio.src = url
        })

        preloadedAudioRef.current.set(type as SoundType, audio)
        console.log(`Audio file preloaded: ${type}`)
      } catch (error) {
        console.warn(`Failed to preload audio file ${type}:`, error)
      }
    }
  }, [])

  // Initialisation complète du système audio
  const initializeAudioSystem = useCallback(async () => {
    if (isInitializedRef.current) return

    console.log('Initializing robust audio system...')

    // 1. Initialiser le contexte audio
    initAudioContext()

    // 2. Précharger les fichiers audio
    await preloadAudioFiles()

    // 3. Marquer comme prêt
    setIsAudioReady(true)
    isInitializedRef.current = true

    console.log('Audio system initialized successfully')
  }, [initAudioContext, preloadAudioFiles])

  // Débloquer l'audio sur interaction utilisateur
  const unlockAudio = useCallback(async () => {
    try {
      // Initialiser le système si pas encore fait
      if (!isInitializedRef.current) {
        await initializeAudioSystem()
      }

      // Débloquer le contexte audio
      if (audioContextRef.current && audioContextRef.current.state === "suspended") {
        await audioContextRef.current.resume()
        console.log("AudioContext resumed successfully")
      }

      // Jouer un son silencieux pour débloquer l'audio sur iOS
      if (audioContextRef.current && isIOSDevice()) {
        try {
          const oscillator = audioContextRef.current.createOscillator()
          const gainNode = audioContextRef.current.createGain()

          gainNode.gain.value = 0.001 // Presque silencieux
          oscillator.connect(gainNode)
          gainNode.connect(audioContextRef.current.destination)

          oscillator.start(0)
          oscillator.stop(0.001)

          console.log("Silent sound played for iOS unlock")
        } catch (e) {
          console.warn("Failed to play silent sound:", e)
        }
      }

      // Tester la lecture audio sur mobile
      if (isMobileDevice()) {
        const testAudio = preloadedAudioRef.current.get('bell')
        if (testAudio) {
          testAudio.volume = 0.001
          try {
            await testAudio.play()
            testAudio.pause()
            testAudio.currentTime = 0
            testAudio.volume = 1.0
            console.log("Mobile audio test successful")
          } catch (e) {
            console.warn("Mobile audio test failed:", e)
          }
        }
      }
    } catch (error) {
      console.error("Failed to unlock audio:", error)
    }
  }, [initializeAudioSystem])

  // Initialiser le contexte audio et précharger les sons
  useEffect(() => {
    // Initialisation différée pour éviter les problèmes de SSR
    const timer = setTimeout(() => {
      initializeAudioSystem()
    }, 100)

    // Nettoyer le contexte audio lors du démontage
    return () => {
      clearTimeout(timer)
      releaseWakeLock()

      if (audioContextRef.current) {
        try {
          if (audioContextRef.current.state !== "closed") {
            audioContextRef.current.close()
          }
        } catch (error) {
          console.error("Error closing AudioContext:", error)
        }
      }
    }
  }, [initializeAudioSystem, releaseWakeLock])

  // Débloquer l'audio sur interaction utilisateur
  useEffect(() => {
    if (typeof window === "undefined") return

    // Ajouter des écouteurs d'événements pour débloquer l'audio
    const unlockEvents = ["touchstart", "touchend", "mousedown", "keydown", "click", "pointerdown"]
    unlockEvents.forEach((event) => {
      document.addEventListener(event, unlockAudio, { once: true, passive: true })
    })

    // Nettoyer les écouteurs d'événements
    return () => {
      unlockEvents.forEach((event) => {
        document.removeEventListener(event, unlockAudio)
      })
    }
  }, [unlockAudio])

  // Fonction robuste pour jouer un son avec HTMLAudioElement
  const playAudioFile = useCallback(async (type: SoundType): Promise<boolean> => {
    const audio = preloadedAudioRef.current.get(type)
    if (!audio) return false

    try {
      // Réinitialiser l'audio
      audio.currentTime = 0
      audio.volume = 1.0

      // Configuration spéciale pour mobile
      if (isMobileDevice()) {
        // Assurer que l'audio n'est pas muet
        audio.muted = false

        // Sur iOS, essayer de jouer immédiatement
        if (isIOSDevice()) {
          audio.load() // Recharger pour s'assurer que c'est prêt
        }
      }

      // Jouer le son avec gestion d'erreur
      await audio.play()
      console.log(`Audio file played successfully: ${type}`)
      return true
    } catch (error) {
      console.warn(`Failed to play audio file ${type}:`, error)
      return false
    }
  }, [])

  // Fonction pour générer un son de type "bell" avec Web Audio API
  const generateBellSound = useCallback(() => {
    if (!audioContextRef.current) return false

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur avec un son plus riche
      oscillator.type = "sine"
      oscillator.frequency.value = 800

      // Configurer l'enveloppe de volume avec attaque plus rapide
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.5, now + 0.01)
      gainNode.gain.linearRampToValueAtTime(0.3, now + 0.1)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.5)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.5)

      // Nettoyer les ressources
      setTimeout(() => {
        try {
          oscillator.disconnect()
          gainNode.disconnect()
        } catch (e) {
          // Ignorer les erreurs de déconnexion
        }
      }, 500)

      console.log("Synthetic bell sound played")
      return true
    } catch (error) {
      console.error("Error generating bell sound:", error)
      return false
    }
  }, [])

  // Fonction pour générer un son de type "alarm" avec Web Audio API
  const generateAlarmSound = useCallback(() => {
    if (!audioContextRef.current) return false

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur avec un son plus urgent
      oscillator.type = "sawtooth"
      oscillator.frequency.value = 1000

      // Créer un effet de pulsation pour l'alarme
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.6, now + 0.05)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.25)
      gainNode.gain.linearRampToValueAtTime(0.6, now + 0.5)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.8)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.8)

      // Nettoyer les ressources
      setTimeout(() => {
        try {
          oscillator.disconnect()
          gainNode.disconnect()
        } catch (e) {
          // Ignorer les erreurs de déconnexion
        }
      }, 800)

      console.log("Synthetic alarm sound played")
      return true
    } catch (error) {
      console.error("Error generating alarm sound:", error)
      return false
    }
  }, [])

  // Fonction pour générer un son de type "notification" avec Web Audio API
  const generateNotificationSound = useCallback(() => {
    if (!audioContextRef.current) return false

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur avec un glissement de fréquence
      oscillator.type = "sine"
      oscillator.frequency.setValueAtTime(1200, now)
      oscillator.frequency.exponentialRampToValueAtTime(600, now + 0.3)

      // Configurer l'enveloppe de volume
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.3, now + 0.02)
      gainNode.gain.linearRampToValueAtTime(0.3, now + 0.1)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.3)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.3)

      // Nettoyer les ressources
      setTimeout(() => {
        try {
          oscillator.disconnect()
          gainNode.disconnect()
        } catch (e) {
          // Ignorer les erreurs de déconnexion
        }
      }, 300)

      console.log("Synthetic notification sound played")
      return true
    } catch (error) {
      console.error("Error generating notification sound:", error)
      return false
    }
  }, [])

  // Fonction robuste pour jouer un son avec stratégies de fallback multiples
  const playSound = useCallback(async (type: SoundType): Promise<void> => {
    if (!isSoundEnabled) {
      console.log(`Sound disabled, skipping ${type} sound`)
      return
    }

    console.log(`Playing ${type} sound with robust audio system`)

    // Demander Wake Lock pour maintenir l'activité
    if (isMobileDevice()) {
      await requestWakeLock()
    }

    // S'assurer que l'AudioContext est actif
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      try {
        await audioContextRef.current.resume()
        console.log("AudioContext resumed before playing sound")
      } catch (error) {
        console.warn("Failed to resume AudioContext:", error)
      }
    }

    let soundPlayed = false

    // Stratégie 1: Essayer le fichier audio préchargé
    if (!soundPlayed) {
      try {
        soundPlayed = await playAudioFile(type)
        if (soundPlayed) {
          console.log(`Successfully played preloaded audio file: ${type}`)
        }
      } catch (error) {
        console.warn(`Failed to play preloaded audio file ${type}:`, error)
      }
    }

    // Stratégie 2: Essayer un nouvel élément Audio (fallback)
    if (!soundPlayed) {
      try {
        const audio = new Audio("/sound/bell.mp3")
        audio.volume = 1.0
        audio.muted = false

        if (isMobileDevice()) {
          audio.load() // Précharger sur mobile
        }

        await audio.play()
        soundPlayed = true
        console.log(`Successfully played new audio element: ${type}`)
      } catch (error) {
        console.warn(`Failed to play new audio element ${type}:`, error)
      }
    }

    // Stratégie 3: Utiliser Web Audio API (fallback final)
    if (!soundPlayed) {
      try {
        switch (type) {
          case "bell":
            soundPlayed = generateBellSound()
            break
          case "alarm":
            soundPlayed = generateAlarmSound()
            break
          case "notification":
            soundPlayed = generateNotificationSound()
            break
        }

        if (soundPlayed) {
          console.log(`Successfully played synthetic sound: ${type}`)
        }
      } catch (error) {
        console.error(`Failed to play synthetic sound ${type}:`, error)
      }
    }

    // Si aucune stratégie n'a fonctionné, essayer une dernière fois avec un son simple
    if (!soundPlayed && audioContextRef.current) {
      try {
        const ctx = audioContextRef.current
        const oscillator = ctx.createOscillator()
        const gainNode = ctx.createGain()

        oscillator.frequency.value = 800
        oscillator.type = "sine"

        gainNode.gain.setValueAtTime(0.5, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.5)

        oscillator.connect(gainNode)
        gainNode.connect(ctx.destination)

        oscillator.start()
        oscillator.stop(ctx.currentTime + 0.5)

        console.log("Emergency fallback sound played")
      } catch (error) {
        console.error("Even emergency fallback failed:", error)
      }
    }

    // Libérer le Wake Lock après un délai
    if (isMobileDevice()) {
      setTimeout(() => {
        releaseWakeLock()
      }, 2000) // Maintenir actif pendant 2 secondes après le son
    }
  }, [isSoundEnabled, playAudioFile, generateBellSound, generateAlarmSound, generateNotificationSound, requestWakeLock, releaseWakeLock])

  const toggleSound = useCallback(() => {
    setIsSoundEnabled(!isSoundEnabled)
  }, [isSoundEnabled])

  const contextValue = {
    playSound,
    isSoundEnabled,
    toggleSound,
    isAudioReady,
    requestWakeLock,
    releaseWakeLock
  }

  return <SoundContext.Provider value={contextValue}>{children}</SoundContext.Provider>
}

export function useSound() {
  const context = useContext(SoundContext)
  if (!context) {
    throw new Error("useSound must be used within a SoundProvider")
  }
  return context
}

